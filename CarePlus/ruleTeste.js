\t\tString statusAttribute=(String) application.getAttributeValue(\"statusAttribute\");\r\n\t\tList enableList=(List) application.getAttributeValue(\"enableValues\");\r\n\t\tList disableList=(List) application.getAttributeValue(\"disableValues\");\r\n\t\t\r\n        for(Object object: processedResponseObject) {\r\n\t\t\r\n\t\t\tMap userMap=(Map)object;\r\n\t\t\tif(userMap.containsKey(statusAttribute)) {\r\n\t\t\t\t\r\n\t\t\t\tString statusValue=((String) userMap.get(statusAttribute)).toLowerCase(); \r\n\t\t\t\r\n\t\t\t\tif(enableList != null) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tfor(String str: enableList) {\r\n\t\t\t\t\t\tif(str.toLowerCase().equalsIgnoreCase(statusValue)) {\r\n\t\t\t\t\t\t\tuserMap.put(\"IIQDisabled\", false);\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(disableList != null) {\r\n\t\t\t\t\tfor(String str: disableList) {\r\n\t\t\t\t\t\tif(str.toLowerCase().equalsIgnoreCase(statusValue)) {\r\n\t\t\t\t\t\t\tuserMap.put(\"IIQDisabled\", true);\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}